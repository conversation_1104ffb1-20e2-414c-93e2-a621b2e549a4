<pop-manager show="{{ show }}" type="center">
  <view class="zan-dialog--container">
    <view wx:if="{{ title }}" class="zan-dialog__header">{{ title }}</view>
    <view class="zan-dialog__content {{ title ? 'zan-dialog__content--title' : '' }}">
      <scroll-view class="zan-dialog__content--scroll" scroll-y="{{ isScroll }}">
        <text>{{ message }}</text>
      </scroll-view>
    </view>
    <view class="zan-dialog__footer {{ buttonsShowVertical ? 'zan-dialog__footer--vertical' : 'zan-dialog__footer--horizon' }}">
      <block wx:for="{{ buttons }}" wx:key="this">
        <zan-button class="zan-dialog__button" custom-class="{{ index === 0 ? 'zan-dialog__button-inside--first' : 'zan-dialog__button-inside' }}" data-type="{{ item.type }}" data-open-type="{{ item.openType }}" open-type="{{ item.openType }}" bind:btnclick="handleButtonClick"
          bind:getuserinfo="handleUserInfoResponse" bind:getphonenumber="handlePhoneResponse" bind:opensetting="handleOpenSettingResponse">
          <view style="color: {{ item.color || '#333' }}">{{ item.text }}</view>
        </zan-button>
      </block>
    </view>
  </view>
</pop-manager>