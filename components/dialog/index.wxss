.zan-dialog--container {
  width: 80vw;
  font-size: 16px;
  overflow: hidden;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
}

.zan-dialog__header {
  padding: 15px 0 0;
  text-align: center;
}

.zan-dialog__content {
  position: relative;
  padding: 15px 20px;
  line-height: 1.5;
  min-height: 40px;
}

.zan-dialog__content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-bottom-width: 1px;
}

.zan-dialog__content--title {
  color: #999;
  font-size: 14px;
}

.zan-dialog__content--scroll {
  max-height: 70vh;
}

.zan-dialog__footer {
  overflow: hidden;
}

.zan-dialog__button {
  -webkit-box-flex: 1;
  flex: 1;
}

.zan-dialog__button-inside, .zan-dialog__button-inside--first {
  margin-bottom: 0;
  line-height: 50px;
  height: 50px;
}

.zan-dialog__button-inside--first::after, .zan-dialog__button-inside::after {
  border-width: 0;
  border-radius: 0;
}

.zan-dialog__footer--horizon {
  display: -webkit-box;
  display: flex;
}

.zan-dialog__footer--horizon .zan-dialog__button-inside::after {
  border-left-width: 1px;
}

.zan-dialog__footer--vertical .zan-dialog__button-inside::after {
  border-top-width: 1px;
}
