<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="custom-class {{ utils.bem('switch', { on: value === activeValue, disabled }) }}"
  style="font-size: {{ size }}; {{ (checked ? activeColor : inactiveColor) ? 'background-color: ' + (checked ? activeColor : inactiveColor ) : '' }}"
  bind:tap="onClick"
>
  <view class="van-switch__node node-class">
    <van-loading wx:if="{{ loading }}" size="50%" custom-class="van-switch__loading" />
  </view>
</view>
