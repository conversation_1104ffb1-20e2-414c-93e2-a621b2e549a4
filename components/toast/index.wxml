<view
  class="zan-toast {{ !message ? 'zan-toast--notitle' : '' }}"
  wx:if="{{ show }}"
  bindtap="clearZanToast"
>
  <!-- icon 展示 -->
  <block
    wx:if="{{ icon || image }}"
  >
    <view
      wx:if="{{ image }}"
      class="zan-toast__icon zan-toast__icon-image"
      style="background-image: url({{ image }});"
    ></view>
    <view
      wx:elif="{{ icon === 'loading' }}"
      class="zan-toast__icon zan-toast__icon-loading"
    >
      <view class="zan-loading"></view>
    </view>
    <zan-icon
      wx:else
      type="{{ icon }}"
      class="zan-toast__icon"
    ></zan-icon>
  </block>

  <!-- 文案展示 -->
  <view wx:if="{{ message }}">{{ message }}</view>
</view>
