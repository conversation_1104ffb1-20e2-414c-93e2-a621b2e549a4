<button
  class="custom-class theme-class zan-btn {{ inGroup ? 'zan-btn--group' : '' }} {{ isLast ? 'zan-btn--last' : '' }} {{size ? 'zan-btn--'+size : ''}} {{size === 'mini' ? 'zan-btn--plain' : ''}} {{plain ? 'zan-btn--plain' : ''}} {{type ? 'zan-btn--'+type : ''}} {{loading ? 'zan-btn--loading' : ''}} {{disabled ? 'zan-btn--disabled' : ''}}"
  disabled="{{ disabled }}"
  hover-class="button-hover"
  open-type="{{ openType }}"
  app-parameter="{{ appParameter }}"
  hover-stop-propagation="{{ hoverStopPropagation }}"
  hover-start-time="{{ hoverStartTime }}"
  hover-stay-time="{{ hoverStayTime }}"
  lang="{{ lang }}"
  session-from="{{ sessionFrom }}"
  send-message-title="{{ sendMessageTitle }}"
  send-message-path="{{ sendMessagePath }}"
  send-message-img="{{ sendMessageImg }}"
  show-message-card="{{ showMessageCard }}"
  bindtap="handleTap"
  bindcontact="bindcontact"
  bindgetuserinfo="bindgetuserinfo"
  bindgetphonenumber="bindgetphonenumber"
  binderror="binderror"
  bindopensetting="bindopensetting"
>
  <slot></slot>
</button>
