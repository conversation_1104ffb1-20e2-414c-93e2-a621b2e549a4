<!-- 商品组 -->
<view class="diy-goods" style="background: {{ itemStyle.background }};">
  <view class="goods-list display__{{ itemStyle.display }} column__{{ itemStyle.column }}">
    <scroll-view scroll-x="{{ itemStyle.display === 'slide' }}">
      <view class="goods-item" wx:for="{{ dataList }}" wx:key="this" wx:for-item="dataItem">
        <form bindsubmit="_onTargetGoods" report-submit="true">
          <button formType="submit" class="btn-normal" data-id="{{ dataItem.goods_id }}">
            <!-- 单列商品 -->
            <block wx:if="{{ itemStyle.column == 1 }}">
              <view class="dis-flex">
                <!-- 商品图片 -->
                <view class="goods-item_left">
                  <image class="image" src="{{ dataItem.image }}"></image>
                </view>
                <view class="goods-item_right">
                  <!-- 商品名称 -->
                  <view wx:if="{{ itemStyle.show.goodsName }}" class="goods-item_title twolist-hidden">
                    <text>{{ dataItem.goods_name }}</text>
                  </view>
                  <view class="goods-item_desc">
                    <!-- 商品卖点 -->
                    <view wx:if="{{ itemStyle.show.sellingPoint }}" class="desc-selling_point dis-flex">
                      <text class="onelist-hidden">{{ dataItem.selling_point }}</text>
                    </view>
                    <!-- 商品销量 -->
                    <view wx:if="{{ itemStyle.show.goodsSales }}" class="desc-goods_sales dis-flex">
                      <text>已售{{ dataItem.goods_sales }}件</text>
                    </view>
                    <!-- 商品价格 -->
                    <view class="desc_footer">
                      <text wx:if="{{ itemStyle.show.goodsPrice }}" class="price_x">¥{{ dataItem.goods_price }}</text>
                      <text class="price_y col-9" wx:if="{{ itemStyle.show.linePrice && dataItem.line_price > 0 }}">¥{{ dataItem.line_price }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            <!-- 多列商品 -->
            <block wx:else>
              <!-- 商品图片 -->
              <view class="goods-image">
                <image class="image" mode="aspectFill" src="{{ dataItem.image }}"></image>
              </view>
              <view class="detail">
                <!-- 商品标题 -->
                <view wx:if="{{ itemStyle.show.goodsName }}" class="goods-name f-28 twolist-hidden">
                  {{ dataItem.goods_name }}
                </view>
                <!-- 商品价格 -->
                <view class="detail-price onelist-hidden">
                  <text wx:if="{{ itemStyle.show.goodsPrice }}" class="goods-price f-30 col-m">￥{{ dataItem.goods_price }}</text>
                  <text wx:if="{{ itemStyle.show.linePrice && dataItem.line_price > 0 }}" class="line-price col-9 f-24">￥{{ dataItem.line_price }}</text>
                </view>
              </view>
            </block>
          </button>
        </form>
      </view>
    </scroll-view>
  </view>
</view>