/* common.wxss */

/* @import "/utils/common.wxss"; */

/* 商品组 */

.diy-goods .goods-list {
  padding: 4rpx;
  box-sizing: border-box;
}

.diy-goods .goods-list .goods-item {
  box-sizing: border-box;
  padding: 6rpx;
}

.diy-goods .goods-list.display__slide {
  white-space: nowrap;
  font-size: 0;
}

.diy-goods .goods-list.display__slide .goods-item {
  display: inline-block;
}

.diy-goods .goods-list.display__list .goods-item {
  float: left;
}

.diy-goods .goods-list.column__2 .goods-item {
  width: 50%;
}

.diy-goods .goods-list.column__3 .goods-item {
  width: 33.33333%;
}

.diy-goods .goods-list .goods-item .goods-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}

.diy-goods .goods-list .goods-item .goods-image:after {
  content: '';
  display: block;
  margin-top: 100%; /* margin 百分比相对父元素宽度计算 */
}

.diy-goods .goods-list .goods-item .goods-image .image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -o-object-fit: cover;
  object-fit: cover;
}

.diy-goods .goods-list .goods-item .detail {
  padding: 8rpx;
  background: #fff;
}

.diy-goods .goods-list .goods-item .detail .goods-name {
  height: 72rpx;
  line-height: 1.3;
  white-space: normal;
}

.diy-goods .goods-list .goods-item .detail .detail-price .goods-price {
  margin-right: 8rpx;
}

.diy-goods .goods-list .goods-item .detail .detail-price .line-price {
  text-decoration: line-through;
}

/* 单列商品 */

.diy-goods .goods-list.column__1 .goods-item {
  width: 100%;
  height: 280rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
}

.diy-goods .goods-list.column__1 .goods-item {
  line-height: 1.6;
}

.diy-goods .goods-list.column__1 .goods-item:last-child {
  margin-bottom: 0;
}

.diy-goods .goods-list.column__1 .goods-item_left {
  display: flex;
  width: 40%;
  background: #fff;
  align-items: center;
}

.diy-goods .goods-list.column__1 .goods-item_left .image {
  display: block;
  width: 240rpx;
  height: 240rpx;
}

.diy-goods .goods-list.column__1 .goods-item_right {
  position: relative;
  width: 60%;
}

.diy-goods .goods-list.column__1 .goods-item_right .goods-item_title {
  height: 72rpx;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.diy-goods .goods-list.column__1 .goods-item_right .goods-item_title {
  line-height: 1.3;
}

.diy-goods .goods-list.column__1 .goods-item_desc {
  margin-top: 8rpx;
}

.diy-goods .goods-list.column__1 .desc-selling_point {
  width: 400rpx;
  /* height: 40rpx; */
  font-size: 24rpx;
  color: #ff495e;
}

.diy-goods .goods-list.column__1 .desc-goods_sales {
  color: #999;
  font-size: 24rpx;
}

.diy-goods .goods-list.column__1 .desc_footer {
  font-size: 24rpx;
}

.diy-goods .goods-list.column__1 .desc_footer .price_x {
  margin-right: 16rpx;
  color: #f03c3c;
  font-size: 30rpx;
}

.diy-goods .goods-list.column__1 .desc_footer .price_y {
  text-decoration: line-through;
}
