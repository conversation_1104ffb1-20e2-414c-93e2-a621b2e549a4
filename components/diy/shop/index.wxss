/* common.wxss */
/* @import "/utils/common.wxss"; */

/* 线下门店 */

.shop-item {
  padding: 16rpx 30rpx;
  min-height: 180rpx;
  font-size: 26rpx;
  line-height: 1.5;
  border-bottom: 1rpx solid #eee;
  box-sizing: border-box;
}

.shop-item__logo {
  margin-right: 30rpx;
}

.shop-item__logo .image {
  display: block;
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  /* box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1); */
}

.shop-item__title {
  font-size: 28rpx;
  color: #fd4a5f;
  margin-bottom: 10rpx;
}

.shop-item__address, .shop-item__phone {
  color: #919396;
}

.shop-item__address {
  width: 520rpx;
}

.shop-item__distance {
  margin-top: 10rpx;
  color: #c1c1c1;
  height: 40rpx;
}

.shop-item__distance .iconfont {
  color: #81838e;
  margin-right: 6rpx;
}
