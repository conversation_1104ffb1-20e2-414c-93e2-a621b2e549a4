/* common.wxss */

/* @import "/utils/common.wxss"; */

.diy-coupon {
  white-space: nowrap;
  font-size: 0;
}

.diy-coupon .coupon-item {
  width: 350rpx;
  height: 130rpx;
  position: relative;
  color: #fff;
  overflow: hidden;
  box-sizing: border-box;
}

.diy-coupon .coupon-item .before {
  content: "";
  position: absolute;
  z-index: 1;
  width: 40rpx;
  height: 40rpx;
  top: 50%;
  left: -.8rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-border-radius: 80%;
  border-radius: 80%;
  background-color: #fff;
}

.diy-coupon .coupon-wrapper {
  display: inline-block;
  padding: 0 12rpx;
}

.diy-coupon .coupon-item .left-content {
  position: relative;
  width: 70%;
  height: 100%;
  background-color: #e5004f;
  float: left;
}

.diy-coupon .coupon-item .left-content .content-top .price {
  font-size: 44rpx;
}

.diy-coupon .coupon-item.color__blue .left-content {
  background: linear-gradient(-125deg, #57bdbf, #2f9de2);
}

.diy-coupon .coupon-item.color__red .left-content {
  background: linear-gradient(-128deg, #ff6d6d, #ff3636);
}

.diy-coupon .coupon-item.color__violet .left-content {
  background: linear-gradient(-113deg, #ef86ff, #b66ff5);
}

.diy-coupon .coupon-item.color__yellow .left-content {
  background: linear-gradient(-141deg, #f7d059, #fdb054);
}

.diy-coupon .coupon-item.color__gray .left-content {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
}

.diy-coupon .coupon-item.color__gray .right-receive {
  background-color: #949494;
}

.diy-coupon .coupon-item .right-receive {
  width: 30%;
  height: 100%;
  background-color: #4e4e4e;
  text-align: center;
  float: right;
}

.diy-coupon .coupon-item .right-receive {
  font-size: 26rpx;
}
