<!-- banner轮播 -->
<view class="diy-banner">
  <swiper autoplay="{{ autoplay }}" class="banner-box swiper-box dot-{{ itemStyle.btnShape }}" duration="{{ duration }}" circular="{{ true }}" indicator-dots="{{ indicatorDots }}" interval="{{ params.interval }}" indicator-color="{{ itemStyle.btnColor }}" indicator-active-color="#000"
    bindchange="_bindChange" data-item-key="{{ itemIndex }}" style="height: {{ imgHeights[imgCurrent] }}rpx">
    <swiper-item wx:for-item="banner" wx:for="{{dataList}}" wx:key="this">
      <image mode="widthFix" catchtap="navigationTo" data-url="{{ banner.linkUrl }}" class="slide-image" bindload="_imagesHeight" src="{{banner.imgUrl}}" data-id="{{index}}" data-item-key="{{ itemIndex }}"></image>
    </swiper-item>
  </swiper>
  <!-- 顶部置灰 -->
  <!-- <view class="linear"></view>  -->
</view>