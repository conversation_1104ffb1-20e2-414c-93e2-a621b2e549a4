/* common.wxss */

/* @import "/utils/common.wxss"; */

/* 导航组 */

.diy-navBar .data-list::after {
  clear: both;
  content: " ";
  display: table;
}

.diy-navBar .item-nav {
  float: left;
  margin: 10px 0;
  text-align: center;
}

.diy-navBar .item-nav .item-image {
  margin-bottom: 4px;
  font-size: 0;
}

.diy-navBar .item-nav .item-image .image {
  width: 88rpx;
  height: 88rpx;
}

/* 分列布局 */

.diy-navBar .avg-sm-3 > .item-nav {
  width: 33.33333333%;
}

.diy-navBar .avg-sm-4 > .item-nav {
  width: 25%;
}

.diy-navBar .avg-sm-5 > .item-nav {
  width: 20%;
}
