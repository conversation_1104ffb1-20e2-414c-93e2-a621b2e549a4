<view class="shortcut" style="right: {{ right }}; bottom: {{ bottom }};">

  <!-- 首页 -->
  <form bindsubmit="_onTargetPage" report-submit="true">
    <button formType="submit" data-index="0" class="nav-item btn-normal {{ isShow ? 'show_80' : (transparent ? '' : 'hide_80') }}">
      <text class="iconfont icon-home"></text>
    </button>
  </form>

  <!-- 分类页 -->
  <form bindsubmit="_onTargetPage" report-submit="true">
    <button formType="submit" data-index="1" class="nav-item btn-normal {{ isShow ? 'show_60' : (transparent ? '' : 'hide_60') }}">
      <text class="iconfont icon-cate"></text>
    </button>
  </form>

  <!-- 购物车 -->
  <form bindsubmit="_onTargetPage" report-submit="true">
    <button formType="submit" data-index="2" class="nav-item btn-normal {{ isShow ? 'show_40' : (transparent ? '' : 'hide_40') }}">
      <text class="iconfont icon-cart"></text>
    </button>
  </form>

  <!-- 个人中心 -->
  <form bindsubmit="_onTargetPage" report-submit="true">
    <button formType="submit" data-index="3" class="nav-item btn-normal {{ isShow ? 'show_20' : (transparent ? '' : 'hide_20') }}">
      <text class="iconfont icon-profile"></text>
    </button>
  </form>

  <!-- 显示隐藏开关 -->
  <form bindsubmit="_onToggleShow" report-submit="true">
    <button formType="submit" class="nav-item nav-item__switch btn-normal {{ isShow ? 'shortcut_click_show' : '' }}">
      <text class='iconfont icon-daohang'></text>
    </button>
  </form>

</view>