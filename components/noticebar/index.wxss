.zan-noticebar {
  display: -webkit-box;
  display: flex;
  /* padding: 4px 10px; */
  font-size: 26rpx;
  line-height: 1.5;
}

.zan-noticebar--within-icon {
  position: relative;
  padding-right: 60rpx;
}

.zan-noticebar__left-icon {
  height: 36rpx;
  min-width: 40rpx;
  padding-top: 1rpx;
  box-sizing: border-box;
}

.zan-noticebar__left-icon>.image {
  width: 32rpx;
  height: 32rpx;
}

.zan-noticebar__right-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 30rpx;
  line-height: 1;
}

.zan-noticebar__content-wrap {
  position: relative;
  -webkit-box-flex: 1;
  flex: 1;
  height: 36rpx;
  overflow: hidden;
}

.zan-noticebar__content {
  position: absolute;
  white-space: nowrap;
}
