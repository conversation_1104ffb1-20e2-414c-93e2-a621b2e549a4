<view wx:if="{{ show }}" class="zan-noticebar {{ hasRightIcon ? 'zan-noticebar--within-icon' : '' }}" style="color: {{ color }};background-color: {{ backgroundColor }}; padding: {{ paddingTop }}px 10px;">
  <view wx:if="{{ leftIcon }}" class="zan-noticebar__left-icon">
    <image class="image" src="{{ leftIcon }}" />
  </view>
  <view class="zan-noticebar__content-wrap">
    <view class="zan-noticebar__content" animation="{{ animationData }}">
      {{ text }}
    </view>
  </view>

  <block wx:if="{{ mode }}">
    <zan-icon wx:if="{{ mode === 'closeable' }}" class="zan-noticebar__right-icon" type="close" bindtap="_handleButtonClick" />
    <navigator wx:if="{{ mode === 'link' }}" url="{{ url }}" open-type="{{ openType }}">
      <zan-icon class="zan-noticebar__right-icon" type="arrow" />
    </navigator>
  </block>

</view>