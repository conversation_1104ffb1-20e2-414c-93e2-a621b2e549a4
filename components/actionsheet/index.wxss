.zan-actionsheet {
  background-color: #f8f8f8;
}

.zan-actionsheet__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}

.zan-actionsheet__container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f8f8;
  -webkit-transform: translate3d(0, 50%, 0);
  transform: translate3d(0, 50%, 0);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  z-index: 11;
  opacity: 0;
  visibility: hidden;
}

.zan-actionsheet__btn {
  margin-bottom: 0 !important;
}

.zan-actionsheet__footer .zan-actionsheet__btn {
  background: #fff;
}

.zan-actionsheet__btn-content {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  flex-direction: row;
  -webkit-box-pack: center;
  justify-content: center;
}

.zan-actionsheet__subname {
  color: #999;
}

.zan-actionsheet__name, .zan-actionsheet__subname {
  height: 45px;
  line-height: 45px;
}

.zan-actionsheet__btn.zan-btn:last-child::after {
  border-bottom-width: 0;
}

.zan-actionsheet__subname {
  margin-left: 2px;
  font-size: 12px;
}

.zan-actionsheet__footer {
  margin-top: 10px;
}

.zan-actionsheet__btn--loading .zan-actionsheet__subname {
  color: transparent;
}

.zan-actionsheet--show .zan-actionsheet__container {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  visibility: visible;
}

.zan-actionsheet--show .zan-actionsheet__mask {
  display: block;
}
