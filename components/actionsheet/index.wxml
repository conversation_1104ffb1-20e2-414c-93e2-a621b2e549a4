<view class="zan-actionsheet {{ show ? 'zan-actionsheet--show' : '' }}">
  <view
    class="mask-class zan-actionsheet__mask"
    bindtap="onMaskClick"
  ></view>
  <view class="container-class zan-actionsheet__container">
    <!-- 选项按钮 -->
    <zan-btn
      wx:for="{{ actions }}"
      wx:key="this"
      bind:btnclick="handleBtnClick"
      data-index="{{ index }}"
      open-type="{{ item.openType }}"
      custom-class="zan-actionsheet__btn"
      loading="{{ item.loading }}"
    >
      <!-- 自定义组件控制 slot 样式有问题，故在 slot 容器上传入 loading 信息 -->
      <view class="zan-actionsheet__btn-content {{ item.loading ? 'zan-actionsheet__btn--loading' : '' }}">
        <view class="zan-actionsheet__name">{{ item.name }}</view>
        <view
          wx:if="{{ item.subname }}"
          class="zan-actionsheet__subname">
          {{ item.subname }}
        </view>
      </view>
    </zan-btn>

    <!-- 关闭按钮 -->
    <view
      wx:if="{{ cancelText }}"
      class="zan-actionsheet__footer"
    >
      <zan-btn
        custom-class="zan-actionsheet__btn"
        catchtap="cancelClick"
      >{{ cancelText }}</zan-btn>
    </view>
  </view>
</view>
