# API接口测试文档

## 测试环境配置

**基础URL**: `https://bonuspoints.uzdns.com/index.php?s=/api/`  
**测试工具**: Postman / curl / 小程序开发工具  

## 接口测试用例

### 1. 用户登录接口测试

#### 测试用例 1.1: 正常登录
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/user/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&code=微信登录code&user_info={\"nickName\":\"测试用户\"}&referee_id="
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "登录成功",
  "data": {
    "token": "用户token",
    "user_id": "用户ID"
  }
}
```

### 2. 获取用户详情测试

#### 测试用例 2.1: 获取用户信息
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/user/detail?wxapp_id=10001&token=用户token" \
  -H "Content-Type: application/json"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "userInfo": {
      "user_id": "用户ID",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "balance": "余额",
      "search_count": "搜题次数"
    }
  }
}
```

### 3. 搜题接口测试

#### 测试用例 3.1: 文字搜题
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/answer.index/search" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token&keys=交通安全法规"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "搜索成功",
  "data": {
    "answer": [
      {
        "question": "题目内容",
        "options": ["A选项", "B选项", "C选项", "D选项"],
        "answer": "A",
        "analysis": "答案解析"
      }
    ]
  }
}
```

### 4. 图片上传测试

#### 测试用例 4.1: 上传图片OCR
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/upload/image" \
  -H "Content-Type: multipart/form-data" \
  -F "iFile=@test_image.jpg" \
  -F "wxapp_id=10001" \
  -F "token=用户token"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "上传成功",
  "data": {
    "file_path": "识别出的文字内容"
  }
}
```

### 5. 卡密相关接口测试

#### 测试用例 5.1: 生成卡密
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/card.index/make" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token&type=10&second=10&number=1"
```

#### 测试用例 5.2: 获取卡密列表
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/card.index/lists?wxapp_id=10001&token=用户token&page=1&state=0" \
  -H "Content-Type: application/json"
```

#### 测试用例 5.3: 使用卡密
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/card.index/useing" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token&code=卡密代码"
```

### 6. 充值接口测试

#### 测试用例 6.1: 获取充值套餐
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/recharge/index?wxapp_id=10001&token=用户token" \
  -H "Content-Type: application/json"
```

#### 测试用例 6.2: 提交充值订单
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/recharge/submit" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token&planId=1"
```

### 7. 文章接口测试

#### 测试用例 7.1: 获取文章分类
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/article/index?wxapp_id=10001" \
  -H "Content-Type: application/json"
```

#### 测试用例 7.2: 获取文章列表
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/article/lists?wxapp_id=10001&page=1&category_id=0" \
  -H "Content-Type: application/json"
```

#### 测试用例 7.3: 获取文章详情
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/article/detail?wxapp_id=10001&article_id=1" \
  -H "Content-Type: application/json"
```

### 8. 配置接口测试

#### 测试用例 8.1: 获取商店配置
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/wxapp/store?wxapp_id=10001" \
  -H "Content-Type: application/json"
```

#### 测试用例 8.2: 获取导航配置
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/wxapp/navigation?wxapp_id=10001" \
  -H "Content-Type: application/json"
```

#### 测试用例 8.3: 获取轮播图
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/banner/index?wxapp_id=10001" \
  -H "Content-Type: application/json"
```

## 错误场景测试

### 1. 无效token测试
```bash
curl -X GET "https://bonuspoints.uzdns.com/index.php?s=/api/user/detail?wxapp_id=10001&token=invalid_token" \
  -H "Content-Type: application/json"
```

**预期结果**:
```json
{
  "code": -1,
  "msg": "登录态失效"
}
```

### 2. 缺少必要参数测试
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/card.index/useing" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token"
```

**预期结果**:
```json
{
  "code": 0,
  "msg": "参数错误"
}
```

### 3. 余额不足测试
```bash
curl -X POST "https://bonuspoints.uzdns.com/index.php?s=/api/card.index/make" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wxapp_id=10001&token=用户token&type=10&second=1000&number=100"
```

**预期结果**:
```json
{
  "code": 0,
  "msg": "余额不足"
}
```

## 性能测试建议

1. **并发测试**: 使用工具模拟多用户同时访问
2. **压力测试**: 测试系统在高负载下的表现
3. **响应时间**: 监控各接口的响应时间
4. **数据库性能**: 监控数据库查询效率

## 测试数据准备

### 测试用户数据
- 测试用户1: 有余额、有搜题次数
- 测试用户2: 无余额、无搜题次数  
- 测试用户3: 新注册用户

### 测试卡密数据
- 有效卡密: 未使用状态
- 无效卡密: 已使用或不存在
- 各种类型卡密: 次卡、月卡、季卡、年卡

### 测试图片数据
- 清晰的题目图片
- 模糊的图片
- 非题目图片
- 大尺寸图片

## 自动化测试脚本

可以使用以下工具进行自动化测试:
- **Postman**: 创建测试集合和环境变量
- **Newman**: 命令行运行Postman测试
- **Jest**: JavaScript测试框架
- **Python requests**: Python HTTP测试库

## 监控和日志

建议在生产环境中添加:
1. API调用日志记录
2. 错误率监控
3. 响应时间监控
4. 用户行为分析
5. 异常告警机制
