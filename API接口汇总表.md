# API接口汇总表

## 接口概览

| 序号 | 接口路径 | 请求方式 | 功能描述 | 是否需要登录 | 调用页面 |
|------|----------|----------|----------|--------------|----------|
| 1 | `user/login` | POST | 用户登录 | ❌ | 登录页 |
| 2 | `user/detail` | GET | 获取用户详情 | ✅ | 首页、个人中心 |
| 3 | `user.index/setWechat` | POST | 设置微信号 | ✅ | 个人中心 |
| 4 | `wxapp/store` | GET | 获取商店配置 | ❌ | 所有页面 |
| 5 | `wxapp/navigation` | GET | 获取导航配置 | ❌ | 首页 |
| 6 | `wxapp/share` | GET | 获取分享配置 | ❌ | 个人中心 |
| 7 | `wxapp/card` | GET | 获取卡密配置 | ❌ | 代理中心 |
| 8 | `banner/index` | GET | 获取轮播图 | ❌ | 首页 |
| 9 | `answer.index/search` | POST | 搜索题目答案 | ✅ | 搜题页面 |
| 10 | `upload/image` | POST | 图片上传OCR | ✅ | 首页拍照 |
| 11 | `exam.index/topic` | GET | 获取练习题目 | ❌ | 模拟考试 |
| 12 | `card.index/make` | POST | 生成卡密 | ✅ | 代理中心 |
| 13 | `card.index/lists` | GET | 获取卡密列表 | ✅ | 代理中心 |
| 14 | `card.index/useing` | POST | 使用卡密 | ✅ | 首页、个人中心 |
| 15 | `recharge/index` | GET | 获取充值套餐 | ✅ | 充值页面 |
| 16 | `recharge/submit` | POST | 提交充值订单 | ✅ | 充值页面、代理中心 |
| 17 | `article/index` | GET | 获取文章分类 | ❌ | 文章首页 |
| 18 | `article/lists` | GET | 获取文章列表 | ❌ | 文章列表 |
| 19 | `article/detail` | GET | 获取文章详情 | ❌ | 文章详情 |

## 按功能模块分类

### 👤 用户模块 (3个接口)
- `user/login` - 用户登录
- `user/detail` - 获取用户详情  
- `user.index/setWechat` - 设置微信号

### ⚙️ 配置模块 (4个接口)
- `wxapp/store` - 商店配置
- `wxapp/navigation` - 导航配置
- `wxapp/share` - 分享配置
- `wxapp/card` - 卡密配置

### 🎯 搜题模块 (3个接口)
- `answer.index/search` - 搜索答案
- `upload/image` - 图片上传
- `exam.index/topic` - 练习题目

### 🎫 卡密模块 (3个接口)
- `card.index/make` - 生成卡密
- `card.index/lists` - 卡密列表
- `card.index/useing` - 使用卡密

### 💰 充值模块 (2个接口)
- `recharge/index` - 充值套餐
- `recharge/submit` - 提交订单

### 📰 文章模块 (3个接口)
- `article/index` - 文章分类
- `article/lists` - 文章列表
- `article/detail` - 文章详情

### 🖼️ 其他模块 (1个接口)
- `banner/index` - 轮播图

## 按页面分类

### 🏠 首页 (`pages/answer/index/index`)
调用接口:
- `wxapp/store` - 获取标题配置
- `wxapp/navigation` - 获取导航按钮
- `banner/index` - 获取轮播图
- `user/detail` - 获取用户信息
- `upload/image` - 拍照上传
- `card.index/useing` - 兑换卡密

### 👤 个人中心 (`pages/answer/user/index`)
调用接口:
- `wxapp/store` - 获取基础配置
- `wxapp/share` - 获取分享配置
- `user/detail` - 获取用户信息
- `user.index/setWechat` - 设置微信号
- `card.index/useing` - 兑换卡密

### 🏢 代理中心 (`pages/answer/agent/index/index`)
调用接口:
- `wxapp/store` - 获取基础配置
- `wxapp/card` - 获取卡密价格配置
- `user/detail` - 获取用户信息
- `card.index/make` - 生成卡密
- `card.index/lists` - 获取卡密列表
- `recharge/submit` - 充值余额

### 🔍 搜题页面 (`pages/answer/callback/index`)
调用接口:
- `answer.index/search` - 搜索题目答案

### 📝 模拟考试 (`pages/answer/exam/index`)
调用接口:
- `exam.index/topic` - 获取练习题目

### 💳 充值页面 (`pages/answer/recharge/index`)
调用接口:
- `wxapp/store` - 获取基础配置
- `recharge/index` - 获取充值套餐
- `recharge/submit` - 提交充值订单

### 📰 文章相关页面
#### 文章列表 (`pages/article/index`)
- `wxapp/store` - 获取基础配置
- `article/index` - 获取文章分类
- `article/lists` - 获取文章列表

#### 文章详情 (`pages/article/detail/index`)
- `article/detail` - 获取文章详情

### 🔐 登录页面 (`pages/login/login`)
调用接口:
- `user/login` - 用户登录

## 接口调用频率统计

### 高频接口 (每次页面加载都会调用)
1. `wxapp/store` - 几乎所有页面都调用
2. `user/detail` - 需要用户信息的页面都调用

### 中频接口 (用户操作触发)
1. `answer.index/search` - 每次搜题
2. `card.index/lists` - 代理中心页面加载和筛选
3. `article/lists` - 文章列表页面和翻页

### 低频接口 (特定操作触发)
1. `user/login` - 仅登录时调用
2. `card.index/make` - 仅生成卡密时调用
3. `recharge/submit` - 仅充值时调用
4. `upload/image` - 仅拍照搜题时调用

## 接口依赖关系

```
user/login (登录) 
    ↓ 获取token
user/detail (获取用户信息)
    ↓ 检查余额/次数
card.index/make (生成卡密) 或 answer.index/search (搜题)
    ↓ 余额/次数不足时
recharge/submit (充值) 或 card.index/useing (使用卡密)
```

## 错误处理统计

| 错误码 | 含义 | 处理方式 | 涉及接口 |
|--------|------|----------|----------|
| 1 | 成功 | 正常处理 | 所有接口 |
| 0 | 业务失败 | 显示错误信息 | 所有接口 |
| -1 | 登录失效 | 跳转登录页 | 需要登录的接口 |

## 性能优化建议

1. **缓存策略**: `wxapp/store`、`wxapp/navigation` 等配置接口可以缓存
2. **分页加载**: `card.index/lists`、`article/lists` 支持分页
3. **图片压缩**: `upload/image` 上传前压缩图片
4. **请求合并**: 相关接口可以考虑合并减少请求次数
5. **预加载**: 首页可以预加载用户常用功能的数据

## 安全注意事项

1. **token验证**: 所有需要登录的接口都要验证token
2. **参数校验**: 服务端要对所有参数进行校验
3. **支付安全**: 充值相关接口要特别注意安全性
4. **文件上传**: 图片上传要限制文件类型和大小
5. **频率限制**: 搜题等接口要做频率限制防止滥用
