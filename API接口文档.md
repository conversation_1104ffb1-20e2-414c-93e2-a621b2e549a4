# 学法减分专业版 - API接口文档

## 项目概述

**项目名称**: 学法减分专业版  
**项目类型**: 微信小程序  
**API基础地址**: `https://bonuspoints.uzdns.com/index.php?s=/api/`  
**请求方式**: GET / POST  
**数据格式**: JSON  

## 全局参数

所有接口请求都会自动添加以下参数：
- `wxapp_id`: 小程序ID (默认: 10001)
- `token`: 用户登录凭证 (从本地存储获取)

## 响应格式

```json
{
  "code": 1,        // 状态码: 1=成功, 0=失败, -1=登录失效
  "msg": "操作成功", // 提示信息
  "data": {}        // 响应数据
}
```

## 接口列表

### 1. 用户相关接口

#### 1.1 用户登录
- **接口路径**: `user/login`
- **请求方式**: POST
- **功能描述**: 微信用户授权登录
- **请求参数**:
  ```json
  {
    "code": "微信登录code",
    "user_info": "用户信息JSON字符串",
    "referee_id": "推荐人ID(可选)"
  }
  ```
- **响应数据**:
  ```json
  {
    "token": "用户登录凭证",
    "user_id": "用户ID"
  }
  ```

#### 1.2 获取用户详情
- **接口路径**: `user/detail`
- **请求方式**: GET
- **功能描述**: 获取当前登录用户的详细信息
- **请求参数**: 无
- **响应数据**: 用户详细信息对象

#### 1.3 设置微信号
- **接口路径**: `user.index/setWechat`
- **请求方式**: POST
- **功能描述**: 设置用户微信号
- **请求参数**:
  ```json
  {
    "wechat": "微信号"
  }
  ```

### 2. 小程序配置接口

#### 2.1 获取商店信息
- **接口路径**: `wxapp/store`
- **请求方式**: GET
- **功能描述**: 获取小程序基本配置信息
- **响应数据**: 包含商店名称、配置等信息

#### 2.2 获取导航配置
- **接口路径**: `wxapp/navigation`
- **请求方式**: GET
- **功能描述**: 获取首页导航配置
- **响应数据**: 导航项列表，包含跳转类型和链接

#### 2.3 获取分享配置
- **接口路径**: `wxapp/share`
- **请求方式**: GET
- **功能描述**: 获取分享相关配置
- **响应数据**: 分享标题、封面等信息

#### 2.4 获取卡密配置
- **接口路径**: `wxapp/card`
- **请求方式**: GET
- **功能描述**: 获取卡密相关配置和价格
- **响应数据**: 各类型卡密的价格配置

### 3. 轮播图接口

#### 3.1 获取轮播图
- **接口路径**: `banner/index`
- **请求方式**: GET
- **功能描述**: 获取首页轮播图列表
- **响应数据**: 轮播图数据列表

### 4. 搜题相关接口

#### 4.1 搜索题目
- **接口路径**: `answer.index/search`
- **请求方式**: POST
- **功能描述**: 根据关键词或图片搜索题目答案
- **请求参数**:
  ```json
  {
    "keys": "搜索关键词或图片路径"
  }
  ```
- **响应数据**: 题目答案信息

#### 4.2 图片上传
- **接口路径**: `upload/image`
- **请求方式**: POST (文件上传)
- **功能描述**: 上传图片进行OCR识别
- **请求参数**: 
  - `iFile`: 图片文件
  - 其他表单参数: `wxapp_id`, `token`
- **响应数据**:
  ```json
  {
    "file_path": "识别出的文字内容"
  }
  ```

### 5. 考试练习接口

#### 5.1 获取练习题目
- **接口路径**: `exam.index/topic`
- **请求方式**: GET
- **功能描述**: 获取随机练习题目
- **响应数据**: 题目列表，包含题目、选项、答案等

### 6. 卡密管理接口

#### 6.1 生成卡密
- **接口路径**: `card.index/make`
- **请求方式**: POST
- **功能描述**: 生成指定类型和数量的卡密
- **请求参数**:
  ```json
  {
    "type": "卡密类型(10=次卡,20=月卡,30=季卡,40=年卡)",
    "second": "次数(次卡专用)",
    "number": "生成数量"
  }
  ```

#### 6.2 卡密列表
- **接口路径**: `card.index/lists`
- **请求方式**: GET
- **功能描述**: 获取卡密列表
- **请求参数**:
  ```json
  {
    "keywords": "搜索关键词(可选)",
    "page": "页码",
    "state": "状态筛选"
  }
  ```
- **响应数据**: 分页的卡密列表

#### 6.3 使用卡密
- **接口路径**: `card.index/useing`
- **请求方式**: POST
- **功能描述**: 使用卡密兑换次数
- **请求参数**:
  ```json
  {
    "code": "卡密代码"
  }
  ```

### 7. 充值相关接口

#### 7.1 获取充值套餐
- **接口路径**: `recharge/index`
- **请求方式**: GET
- **功能描述**: 获取充值套餐列表
- **响应数据**: 充值套餐列表，包含价格、赠送等信息

#### 7.2 提交充值订单
- **接口路径**: `recharge/submit`
- **请求方式**: POST
- **功能描述**: 创建充值订单并获取支付参数
- **请求参数**:
  ```json
  {
    "planId": "套餐ID(可选)",
    "customMoney": "自定义金额(可选)"
  }
  ```
- **响应数据**:
  ```json
  {
    "payment": {
      "timeStamp": "时间戳",
      "nonceStr": "随机字符串", 
      "prepay_id": "预支付ID",
      "paySign": "支付签名"
    },
    "msg": {
      "success": "支付成功提示",
      "error": "支付失败提示"
    }
  }
  ```

### 8. 文章相关接口

#### 8.1 获取文章首页
- **接口路径**: `article/index`
- **请求方式**: GET
- **功能描述**: 获取文章分类列表
- **响应数据**: 文章分类列表

#### 8.2 获取文章列表
- **接口路径**: `article/lists`
- **请求方式**: GET
- **功能描述**: 获取指定分类的文章列表
- **请求参数**:
  ```json
  {
    "page": "页码",
    "category_id": "分类ID"
  }
  ```
- **响应数据**: 分页的文章列表

#### 8.3 获取文章详情
- **接口路径**: `article/detail`
- **请求方式**: GET
- **功能描述**: 获取文章详细内容
- **请求参数**:
  ```json
  {
    "article_id": "文章ID"
  }
  ```
- **响应数据**: 文章详细信息，包含标题、内容等

## 错误码说明

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 1 | 请求成功 | 正常处理返回数据 |
| 0 | 请求失败 | 显示错误信息 |
| -1 | 登录态失效 | 跳转到登录页面重新授权 |

## 页面路由说明

### 小程序页面结构
```
pages/
├── answer/                 # 答题相关页面
│   ├── index/index        # 首页 (tabBar)
│   ├── user/index         # 个人中心 (tabBar)
│   ├── agent/index/index  # 代理中心
│   ├── callback/index     # 搜题结果页
│   ├── exam/index         # 模拟练习
│   ├── help/index         # 帮助中心
│   ├── recharge/index     # 充值页面
│   ├── webview/index      # 内嵌网页
│   └── user/v2           # 用户中心V2
├── article/               # 文章相关页面
│   ├── index             # 文章列表
│   └── detail/index      # 文章详情
└── login/
    └── login             # 登录授权页面
```

### TabBar配置
- **首页**: `pages/answer/index/index` - 主要功能入口
- **我的**: `pages/answer/user/index` - 个人中心和工具

## 业务流程说明

### 1. 用户登录流程
1. 用户点击登录按钮
2. 调用 `wx.getUserProfile()` 获取用户信息
3. 调用 `wx.login()` 获取登录code
4. 调用 `user/login` 接口完成登录
5. 存储 `token` 和 `user_id` 到本地

### 2. 搜题流程
1. **文字搜索**: 用户输入关键词 → 调用 `answer.index/search`
2. **拍照搜题**: 选择图片 → 上传到 `upload/image` → OCR识别 → 调用 `answer.index/search`

### 3. 卡密生成和使用流程
1. **生成卡密**: 选择类型和数量 → 调用 `card.index/make` → 扣除余额
2. **使用卡密**: 输入卡密代码 → 调用 `card.index/useing` → 增加使用次数

### 4. 充值流程
1. 选择充值套餐或输入自定义金额
2. 调用 `recharge/submit` 获取支付参数
3. 调用微信支付API完成支付
4. 支付成功后更新用户余额

## 数据模型说明

### 用户信息模型
```javascript
{
  user_id: "用户ID",
  nickname: "用户昵称",
  avatar: "头像URL",
  balance: "账户余额",
  search_count: "剩余搜题次数",
  wechat: "微信号"
}
```

### 卡密信息模型
```javascript
{
  card_id: "卡密ID",
  code: "卡密代码",
  type: "类型(10=次卡,20=月卡,30=季卡,40=年卡)",
  value: "价值/次数",
  status: "状态(0=未使用,1=已使用)",
  create_time: "创建时间",
  use_time: "使用时间"
}
```

### 题目信息模型
```javascript
{
  question: "题目内容",
  options: ["选项A", "选项B", "选项C", "选项D"],
  answer: "正确答案",
  analysis: "答案解析"
}
```

## 注意事项

1. 所有接口都需要传递 `wxapp_id` 参数
2. 需要登录的接口会自动验证 `token` 参数
3. 登录态失效时会自动跳转到登录页面
4. 文件上传接口使用 `multipart/form-data` 格式
5. 其他接口使用 `application/json` 或 `application/x-www-form-urlencoded` 格式
6. GET请求使用 `application/json` 头部
7. POST请求使用 `application/x-www-form-urlencoded` 头部
8. 支付相关接口需要特别注意安全性
9. 图片上传有大小和格式限制
10. 分页接口支持上拉加载更多

## 开发环境配置

### 本地开发
- 修改 `siteinfo.js` 中的 `siteroot` 为本地API地址
- 确保小程序开发工具中开启"不校验合法域名"

### 生产环境
- API域名: `https://bonuspoints.uzdns.com/`
- 需要在微信公众平台配置服务器域名白名单

## 版本信息
- 当前版本: 见 `version.json` 文件
- 最后更新: 2024年
- 维护状态: 活跃开发中
